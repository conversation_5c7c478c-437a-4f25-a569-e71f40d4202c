# main.py

import uvicorn
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from api.dataset.evaluation_case_pool import router as evaluation_case_pool_router
from api.evaluation_pipeline.inference_config import router as inference_config_router
from api.evaluation_pipeline.evaluation_metrics import router as evaluation_metrics_router
# 在现有路由之后添加
from api.annotation.pdp_annotation import router as pdp_annotation_router
#from api.annotation.pdp_clip_annotation import router as pdp_clip_annotation_router

from api.auth.auth import router as auth_router
from api.evaluation_pipeline.inference_process import router as inference_process_router
from api.visualize.pickle_visualize import router as pickle_visualize_router
from api.dataset.evaluation_sets import router as evaluation_sets_router
from database.db_operations import initialize_system
from api.validity_check.validity_check import router as validity_check_router
from api.data_process.data_process import router as data_process_router
from api.annotation.path_pair_annotation import router as path_pair_annotation_router
from api.annotation.pdp_lane_scene_annotation import router as pdp_lane_scene_annotation_router
# from api.annotation.dlp_annotation import router as dlp_annotation_router

# 创建主应用
app = FastAPI()


@app.on_event("startup")
async def startup_event():
    initialize_system()
# 配置跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
# app.mount("/static", StaticFiles(directory="/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/static"), name="static")

# 包含路由器
app.include_router(auth_router)
app.include_router(evaluation_case_pool_router)
app.include_router(evaluation_sets_router)
app.include_router(inference_config_router)
app.include_router(inference_process_router)
app.include_router(pickle_visualize_router)
app.include_router(pdp_annotation_router)
app.include_router(evaluation_metrics_router)
app.include_router(validity_check_router)
app.include_router(path_pair_annotation_router)
app.include_router(data_process_router)
app.include_router(pdp_lane_scene_annotation_router)
# app.include_router(dlp_annotation_router)
#app.include_router(pdp_clip_annotation_router)
# 如果需要，可以添加根路由


@app.get("/")
async def root():
    return {"message": "欢迎使用评测平台API"}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=5006, reload=False, workers=8)
    # uvicorn.run("main:app", host="0.0.0.0", port=3006, reload=False, workers=8)
