from fastapi import APIRouter, HTTPException, Body, Path, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime, date
import os
from database.db_operations import execute_query
from api.visualize.pickle_visualize import load_pickle, extract_geometry_from_pickle
from api.auth.auth import get_current_user
import cv2
import numpy as np
import base64
from api.visualize.calib_reader import CalibReader, CalibReaderDict

router = APIRouter(prefix="/api/annotation", tags=["dlp_annotation"])


class DlpPathAnnotation(BaseModel):
    pkl_id: int
    traj_index: int
    annotation: str  # "0/1/2/3/4/5"
    inference_config_id: Optional[int] = 3
    delete_annotation: Optional[bool] = False
    evaluation_set_id: Optional[int] = None


@router.get("/dlp/{evaluation_set_id}/cases")
async def get_evaluation_set_cases(
    evaluation_set_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
):
    """获取评测集中的PKL文件列表"""
    try:
        offset = (page - 1) * page_size

        query = """
        SELECT DISTINCT ecp.id, ecp.pkl_dir, ecp.pkl_name, ecp.vehicle_type, 
               ecp.vin, ecp.time_ns, ecp.key_obs_id, ecp.dirty_data, 
               ecp.created_at, esc.is_checked
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case esc ON ecp.id = esc.case_id
        WHERE esc.evaluation_set_id = %s
        ORDER BY ecp.time_ns
        LIMIT %s OFFSET %s
        """

        result = execute_query(
            query, (evaluation_set_id, page_size, offset), fetch_all=True
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"查询失败: {result['error']}")

        # 获取总数
        count_query = """
        SELECT COUNT(DISTINCT ecp.id)
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case esc ON ecp.id = esc.case_id
        WHERE esc.evaluation_set_id = %s
        """
        count_result = execute_query(count_query, (evaluation_set_id,), fetch_one=True)
        total = count_result["data"][0] if count_result["success"] else 0

        cases = []
        if result["data"]:
            for row in result["data"]:
                cases.append(
                    {
                        "id": row[0],
                        "pkl_dir": row[1],
                        "pkl_name": row[2],
                        "vehicle_type": row[3],
                        "vin": row[4],
                        "time_ns": row[5],
                        "key_obs_id": row[6],
                        "dirty_data": bool(row[7]),
                        "created_at": row[8],
                        "is_checked": bool(row[9]),
                    }
                )

        return {
            "success": True,
            "cases": cases,
            "total": total,
            "page": page,
            "page_size": page_size,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取案例列表失败: {str(e)}")


@router.get("/dlp-paths/{pkl_id}")
async def get_dlp_paths(
    pkl_id: int,
    evaluation_set_id: Optional[int] = Query(None, description="评测集ID"),
):
    """获取指定PKL文件中的所有DLP路径信息"""
    # 查询pkl文件路径
    query = """
    SELECT pkl_dir, pkl_name, dirty_data, vin FROM evaluation_case_pool WHERE id = %s
    """
    result = execute_query(query, (pkl_id,), fetch_one=True)

    if not result["success"] or not result["data"]:
        raise HTTPException(status_code=404, detail="找不到指定的PKL文件")

    pkl_dir, pkl_name, is_dirty, vin = result["data"]
    pickle_path = os.path.join(pkl_dir, pkl_name)

    if not os.path.exists(pickle_path):
        raise HTTPException(status_code=404, detail="PKL文件不存在")

    try:
        # 加载pickle文件并提取DLP路径数据
        data = load_pickle(pickle_path)
        ego_v = data["ego_input"][0] ** 2 + data["ego_input"][1] ** 2
        ego_v = ego_v**0.5
        ego_s = ego_v * 6
        ego_default_s = 40
        ego_final_s = max(ego_s, ego_default_s)
        ego_final_index = int(ego_final_s / 2)
        pdp_paths = data.get("pdp_path", [])
        pdp_trajs =data.get("pdp_traj", [])
        # np.ndarray(200,2)
        ego_gt = data.get("path_point", [])
        ego_gt_mask = data.get("path_mask", [])
        # 查询现有标注
        if evaluation_set_id is not None:
            query = """
            SELECT  traj_index, annotation
            FROM dlp_path_annotation 
            WHERE pkl_id = %s AND evaluation_set_id = %s
            """
            annotations_result = execute_query(
                query, (pkl_id, evaluation_set_id), fetch_all=True
            )
        else:
            query = """
            SELECT  traj_index, annotation
            FROM dlp_path_annotation 
            WHERE pkl_id = %s
            """
            annotations_result = execute_query(query, (pkl_id,), fetch_all=True)

        # 转换为字典
        annotations = {}
        if annotations_result["success"] and annotations_result["data"]:
            for  traj_index, annotation in annotations_result["data"]:
                annotations[traj_index] = {"annotation": annotation}

        # 准备返回数据 - 这里需要根据实际的DLP数据结构调整
        paths_info = []
        # TODO: 根据实际DLP数据结构提取路径信息
        # 准备返回数据,同时包含路径点坐标

        if ego_gt is not None and ego_gt_mask is not None and ego_gt_mask[-1] >= 0.5:
            if len(ego_gt) > 50:
                step = max(1, len(ego_gt) // 50)
                # ego_gt0 = ego_gt[:4]
                ego_gt = ego_gt[::step]
                # ego_gt = ego_gt0 + ego_gt1
            gt_visualization_points = []
            gt_middle_point = None
            for i, (point, mask) in enumerate(zip(ego_gt, ego_gt_mask)):
                if mask >= 0.5:  # 有效点
                    if i == ego_final_index:
                        gt_middle_point = [float(point[0]), float(point[1]), 0.0]
                    gt_visualization_points.append(
                        [float(point[0]), float(point[1]), 0.0]
                    )
            # gt_visualization_points 下采样到50个点 当前200个

            # print('..gt path',gt_visualization_points[-1][0],gt_visualization_points[-1][1])
            if len(gt_visualization_points) > 0:
                gt_path_info = {
                    "index": -1,  # GT路径使用-1作为索引
                    "probability": 1.0,  # GT路径概率设为1.0
                    "points_count": len(gt_visualization_points),
                    "annotation": annotations.get(-1, None),  # 查找GT路径的标注
                    "visualization_points": gt_visualization_points,
                    "middle_point": gt_middle_point,
                    "is_ground_truth": True,  # 标识为GT路径
                }
                paths_info.append(gt_path_info)
        for i, path in enumerate(pdp_paths):
            middle_point = None
            prob = float(path.get("prob", 0))
            # if prob == 0.0:
            #     continue
            raw_points = path.get("raw_points", [])
            points_count = len(raw_points)

            # 提取可视化所需的点数据
            visualization_points = []
            index = 0
            for point in raw_points:
                if index == ego_final_index:
                    middle_point = point
                # 确保每个点至少有x,y坐标
                if len(point) >= 2:
                    # 将点转换为[x,y,z]格式,如果没有z坐标则默认为0
                    visualization_point = [
                        float(point[0]),
                        float(point[1]),
                        float(point[2]) if len(point) > 2 else 0.0,
                    ]
                    visualization_points.append(visualization_point)
                index += 1

            path_info = {
                "index": i,
                "probability": prob,
                "points_count": points_count,
                "annotation": annotations.get(i, None),
                "visualization_points": visualization_points,  # 添加可视化点数据
                "middle_point": middle_point,
                "is_ground_truth": False,  # 标识为非GT路径
            }
            paths_info.append(path_info)
        trajs_info=[]
        for i, path in enumerate(pdp_trajs[:6]):
            middle_point = None
            prob = float(path.get("prob", 0))
            vel=path.get("vel",[])
            acc=path.get("acc",[])
            s=path.get("s",[])
            

            traj_info = {
                "index": i,
                "probability": prob,
                "vel":vel,
                "acc":acc,
                "s":s,
                "annotation": annotations.get(i, None),
                "is_ground_truth": False,  # 标识为非GT路径
            }
            trajs_info.append(traj_info)
        return {
            "success": True,
            "paths": paths_info,
            "trajs":trajs_info,
            "is_dirty": is_dirty,
            "vin": vin,
            # "image_data": None,  # TODO: 添加图像数据
            # "ego2img": None,  # TODO: 添加变换矩阵
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PKL文件时出错: {str(e)}")


@router.post("/dlp-paths")
async def annotate_dlp_path(
    annotation: DlpPathAnnotation,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """标注DLP路径"""
    if annotation.annotation not in ["0", "1", "2","3","4","5"]:
        raise HTTPException(
            status_code=400, detail="标注值必须是 0/1/2/3/4/5"
        )

    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (annotation.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    if annotation.delete_annotation:
        # 删除标注
        delete_query = """
        DELETE FROM dlp_path_annotation 
        WHERE pkl_id = %s AND traj_index = %s AND evaluation_set_id = %s
        """
        result = execute_query(
            delete_query,
            (annotation.pkl_id, annotation.traj_index, annotation.evaluation_set_id),
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"删除标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注已删除"}
    else:
        # 插入或更新标注
        if current_user:
            query = """
            INSERT INTO dlp_path_annotation 
            (pkl_id,  traj_index, annotation, inference_config_id, evaluation_set_id, 
             employee_id, created_at, updated_at) 
            VALUES (%s, %s,%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE 
            annotation = VALUES(annotation),
            employee_id = VALUES(employee_id),
            updated_at = CURRENT_TIMESTAMP
            """
            params = (
                annotation.pkl_id,
                annotation.traj_index,
                annotation.annotation,
                annotation.inference_config_id,
                annotation.evaluation_set_id,
                current_user.get("employee_id", ""),
            )
        else:
            query = """
            INSERT INTO dlp_path_annotation 
            (pkl_id,  traj_index, annotation, inference_config_id, evaluation_set_id, 
             created_at, updated_at) 
            VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE 
            annotation = VALUES(annotation),
            updated_at = CURRENT_TIMESTAMP
            """
            params = (
                annotation.pkl_id,
                annotation.traj_index,
                annotation.annotation,
                annotation.inference_config_id,
                annotation.evaluation_set_id,
            )

        result = execute_query(query, params)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"保存标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注保存成功"}


@router.get("/export-dlp-annotations/{evaluation_set_id}")
async def export_dlp_annotations(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """导出指定评测集下的DLP标注结果"""
    try:
        # 检查评测集ID是否存在
        check_set_query = "SELECT id FROM evaluation_set WHERE id = %s"
        set_exists_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )

        if not set_exists_result["success"] or not set_exists_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        query = """
        SELECT 
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            a.path_index,
            a.annotation,
            a.inference_config_id,
            a.updated_at,
            a.employee_id
        FROM 
            dlp_path_annotation a
        JOIN 
            evaluation_case_pool p ON a.pkl_id = p.id
        WHERE 
            a.evaluation_set_id = %s
        ORDER BY 
            pickle_path, a.path_index
        """

        result = execute_query(query, (evaluation_set_id,), fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据
        annotations_by_path = {}
        if result["data"]:
            for row in result["data"]:
                (
                    pickle_path,
                    path_index,
                    annotation,
                    inference_config_id,
                    updated_at,
                    employee_id,
                ) = row

                if pickle_path not in annotations_by_path:
                    annotations_by_path[pickle_path] = []

                annotations_by_path[pickle_path].append(
                    {
                        "path_index": path_index,
                        "annotation": annotation,
                        "inference_config_id": inference_config_id,
                        "updated_at": updated_at.isoformat() if updated_at else None,
                        "employee_id": employee_id,
                    }
                )

        return {
            "success": True,
            "evaluation_set_id": evaluation_set_id,
            "annotations": annotations_by_path,
            "total_files": len(annotations_by_path),
            "export_time": datetime.now().isoformat(),
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")
