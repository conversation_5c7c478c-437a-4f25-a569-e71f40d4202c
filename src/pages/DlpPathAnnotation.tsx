import React, { useState, useEffect } from 'react';
import { Layout, List, Card, Button, Input, Pagination, Tag, Space, message, Spin, Radio, Tabs, Modal, Typography, Form } from 'antd';
import { CopyOutlined, SearchOutlined, ExportOutlined, CheckOutlined, CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined, CommentOutlined, WarningOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import PickleVisualizer from '../components/PickleVisualizer';
import ResizableSider from '../components/ResizableSider';
import InferenceConfigSelector from '../components/InferenceConfigSelector';
import ImageWithPaths from '../components/ImageWithPaths';
import { useAuth } from '../contexts/AuthContext';
import './PdpPathAnnotation.css';

const { Content } = Layout;
const { Text } = Typography;

interface EvaluationCase {
    id: number;
    pkl_dir: string;
    pkl_name: string;
    vehicle_type: string;
    vin: string;
    time_ns: number;
    key_obs_id: string;
    dirty_data: boolean;
    created_at: string;
    is_checked: boolean;
}

interface DlpPathInfo {
    index: number;
    annotation?: {
        annotation: string;
    };
}

const DlpPathAnnotation: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const { user, isAuthenticated } = useAuth();
    
    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(400);
    const [rightSiderWidth, setRightSiderWidth] = useState(250);
    const fixedCenterWidth = 650;
    
    const [loading, setLoading] = useState({
        pklList: false,
        paths: false,
        annotation: false,
        visualization: false,
    });
    
    const [pklList, setPklList] = useState<EvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
    const [dlpPaths, setDlpPaths] = useState<Record<number, DlpPathInfo>>({});
    const [highlightPathIndex, setHighlightPathIndex] = useState<number | null>(null);
    const [imageData, setImageData] = useState<string | null>(null);
    const [ego2img, setEgo2img] = useState<number[][] | null>(null);
    const [egoYaw, setEgoYaw] = useState<number | null>(null);
    const [isDirtyData, setIsDirtyData] = useState(false);
    const [hasDlpPaths, setHasDlpPaths] = useState(false);
    
    const [annotationStats, setAnnotationStats] = useState({
        total: 0,
        annotated: 0,
        good: 0,
        bad: 0,
        unknown: 0,
    });

    // 加载PKL列表
    const loadPklList = async () => {
        if (!id) return;
        
        setLoading(prev => ({ ...prev, pklList: true }));
        try {
            const response = await axios.get(`/api/annotation/dlp/${id}/cases`);
            if (response.data.success) {
                setPklList(response.data.cases || []);
            }
        } catch (error) {
            console.error('Failed to load PKL list:', error);
            message.error('加载PKL列表出错');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };

    // 处理PKL选择
    const handlePklSelect = async (pkl: EvaluationCase) => {
        setSelectedPkl(pkl);
        setHighlightPathIndex(null);
        setImageData(null);
        setLoading(prev => ({ ...prev, paths: true }));

        try {
            const response = await axios.get(`/api/annotation/dlp-paths/${pkl.id}`, {
                params: {
                    evaluation_set_id: id
                }
            });
            if (response.data.success) {
                if (response.data.paths && response.data.paths.length > 0) {
                    const pathsDict: Record<number, DlpPathInfo> = {};
                    response.data.paths.forEach((path: DlpPathInfo) => {
                        pathsDict[path.index] = path;
                    });
                    setDlpPaths(pathsDict);
                    setIsDirtyData(response.data.is_dirty || false);
                    setHasDlpPaths(true);

                    if (response.data.image_data) {
                        setImageData(response.data.image_data);
                    }
                    if (response.data.ego2img) {
                        setEgo2img(response.data.ego2img);
                    }
                } else {
                    setDlpPaths({});
                    setHasDlpPaths(false);
                }
            }
        } catch (error) {
            console.error('Failed to load DLP paths:', error);
            message.error('加载DLP路径失败');
        } finally {
            setLoading(prev => ({ ...prev, paths: false }));
        }
    };

    // 高亮路径
    const handleHighlightPath = (pathIndex: number) => {
        setHighlightPathIndex(pathIndex === highlightPathIndex ? null : pathIndex);
    };

    // 提交标注
    const handleAnnotate = async (pathIndex: number, annotation: string) => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        const currentAnnotation = dlpPaths[pathIndex]?.annotation?.annotation;
        const isDeleteAction = currentAnnotation === annotation;

        setLoading(prev => ({ ...prev, annotation: true }));
        try {
            const response = await axios.post('/api/annotation/dlp-paths', {
                pkl_id: selectedPkl.id,
                path_index: pathIndex,
                annotation,
                delete_annotation: isDeleteAction,
                evaluation_set_id: id,
                employee_id: user?.employee_id || null,
            });

            if (response.data.success) {
                // 更新本地状态
                setDlpPaths(prev => ({
                    ...prev,
                    [pathIndex]: {
                        ...prev[pathIndex],
                        annotation: isDeleteAction ? undefined : { annotation }
                    }
                }));
                message.success(isDeleteAction ? '标注已删除' : '标注保存成功');
            }
        } catch (error) {
            console.error('Annotation failed:', error);
            message.error('标注失败');
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 导出标注
    const handleExportAnnotations = async () => {
        if (!id) {
            message.error('评测集ID无效');
            return;
        }

        try {
            const exportUrl = `/api/annotation/export-dlp-annotations/${id}`;
            window.open(exportUrl, '_blank');
        } catch (error) {
            console.error('Export failed:', error);
            message.error('导出失败');
        }
    };

    // 初始加载
    useEffect(() => {
        if (id) {
            loadPklList();
        }
    }, [id]);

    useEffect(() => {
        if (!isAuthenticated) {
            message.error('请先登录后再进行标注操作');
            return;
        }
    }, [isAuthenticated]);

    return (
        <Layout style={{ height: '100vh', overflow: 'hidden' }}>
            {/* 左侧PKL列表 */}
            <ResizableSider
                width={leftSiderWidth}
                onResize={setLeftSiderWidth}
                minWidth={300}
                maxWidth={600}
                position="left"
            >
                <div className="pkl-list-container">
                    <div className="pkl-list-header">
                        <Space>
                            <Text strong>DLP标注 - PKL列表</Text>
                            <Button 
                                icon={<ExportOutlined />} 
                                onClick={handleExportAnnotations}
                                size="small"
                            >
                                导出
                            </Button>
                        </Space>
                    </div>
                    
                    <div className="pkl-list-content">
                        {loading.pklList ? (
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <Spin tip="加载中..." />
                            </div>
                        ) : (
                            <List
                                dataSource={pklList}
                                renderItem={(pkl) => (
                                    <List.Item
                                        className={selectedPkl?.id === pkl.id ? 'selected' : ''}
                                        onClick={() => handlePklSelect(pkl)}
                                        style={{ cursor: 'pointer' }}
                                    >
                                        <Card size="small" style={{ width: '100%' }}>
                                            <div>
                                                <Text strong>{pkl.pkl_name}</Text>
                                                <br />
                                                <Text type="secondary">VIN: {pkl.vin}</Text>
                                                {pkl.dirty_data && (
                                                    <Tag color="orange" style={{ marginLeft: 8 }}>
                                                        脏数据
                                                    </Tag>
                                                )}
                                            </div>
                                        </Card>
                                    </List.Item>
                                )}
                            />
                        )}
                    </div>
                </div>
            </ResizableSider>

            {/* 中间可视化区域 */}
            <div 
                className="center-content"
                style={{ 
                    width: `${fixedCenterWidth}px`,
                    minWidth: `${fixedCenterWidth}px`,
                    maxWidth: `${fixedCenterWidth}px`,
                }}
            >
                {selectedPkl ? (
                    <div className="visualization-container" style={{ width: '100%', height: '100%' }}>
                        <div className="visualization-area" style={{ width: '100%', height: '100%' }}>
                            {loading.visualization ? (
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'center', 
                                    alignItems: 'center', 
                                    height: '100%' 
                                }}>
                                    <Spin tip="加载可视化..." />
                                </div>
                            ) : (
                                <div style={{ width: '100%', height: '100%' }}>
                                    {imageData && (
                                        <div className="e2e-image-container" style={{ 
                                            width: '100%', 
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            marginBottom: '10px'
                                        }}>
                                            <ImageWithPaths
                                                imageData={imageData}
                                                pdpPaths={dlpPaths}
                                                highlightPathIndex={highlightPathIndex}
                                                ego2img={ego2img}
                                                egoYaw={egoYaw || 0}
                                                style={{ 
                                                    maxWidth: '100%',
                                                    maxHeight: '200px'
                                                }}
                                            />
                                        </div>
                                    )}
                                    <div style={{ 
                                        width: '100%', 
                                        height: imageData ? 'calc(100% - 220px)' : '100%',
                                        minHeight: '400px'
                                    }}>
                                        <PickleVisualizer
                                            evaluationCase={selectedPkl}
                                            highlightPathIndex={highlightPathIndex}
                                            pdpPaths={dlpPaths}
                                            height={imageData ? 'calc(100vh - 320px)' : '60vh'}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        color: '#999'
                    }}>
                        <Text>请从左侧选择一个PKL文件</Text>
                    </div>
                )}
            </div>

            {/* 右侧路径列表 */}
            <ResizableSider
                width={rightSiderWidth}
                onResize={setRightSiderWidth}
                minWidth={200}
                maxWidth={400}
                position="right"
            >
                <div className="paths-panel">
                    <div className="paths-header">
                        <Text strong>DLP路径列表</Text>
                    </div>
                    
                    {loading.paths ? (
                        <div style={{ textAlign: 'center', padding: '20px' }}>
                            <Spin tip="加载路径..." />
                        </div>
                    ) : Object.keys(dlpPaths).length === 0 ? (
                        <div className="no-paths">
                            <Text>未找到DLP路径数据</Text>
                        </div>
                    ) : (
                        <div className="paths-list">
                            {Object.values(dlpPaths).sort((a, b) => a.index - b.index).map((path) => (
                                <div className="path-card-container" key={path.index}>
                                    <Card
                                        className={highlightPathIndex === path.index ? 'path-card highlighted' : 'path-card'}
                                        size="small"
                                        onClick={() => handleHighlightPath(path.index)}
                                        hoverable
                                    >
                                        <div className="path-info">
                                            <div className="path-title">
                                                <Tag color="blue">路径 {path.index + 1}</Tag>
                                            </div>
                                        </div>
                                    </Card>

                                    <div className="path-actions" onClick={e => e.stopPropagation()}>
                                        <Radio.Group
                                            value={path.annotation?.annotation}
                                            onChange={e => {
                                                e.stopPropagation();
                                                handleAnnotate(path.index, e.target.value);
                                            }}
                                            buttonStyle="solid"
                                        >
                                            <Radio.Button
                                                value="good"
                                                className={path.annotation?.annotation === 'good' ? 'good-selected' : ''}
                                            >
                                                <CheckCircleOutlined /> 好
                                            </Radio.Button>
                                            <Radio.Button
                                                value="bad"
                                                className={path.annotation?.annotation === 'bad' ? 'bad-selected' : ''}
                                            >
                                                <CloseCircleOutlined /> 差
                                            </Radio.Button>
                                            <Radio.Button
                                                value="unknown"
                                                className={path.annotation?.annotation === 'unknown' ? 'unknown-selected' : ''}
                                            >
                                                <QuestionCircleOutlined /> 未知
                                            </Radio.Button>
                                        </Radio.Group>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </ResizableSider>
        </Layout>
    );
};

export default DlpPathAnnotation;