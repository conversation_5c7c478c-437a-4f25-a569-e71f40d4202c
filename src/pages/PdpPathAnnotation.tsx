import React, { useState, useEffect } from 'react';
import { Layout, List, Card, Button, Input, Pagination, Tag, Space, message, Spin, Radio, Tabs, Modal, Typography, Form } from 'antd';
import { CopyOutlined, SearchOutlined, ExportOutlined, CheckOutlined, CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined, CommentOutlined, WarningOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import PickleVisualizer from '../components/PickleVisualizer';
import ResizableSider from '../components/ResizableSider';
import InferenceConfigSelector from '../components/InferenceConfigSelector';
import ImageWithPaths from '../components/ImageWithPaths';
import './PdpPathAnnotation.css';
import { EvaluationCase, EvaluationSet, PdpPathInfo, InferenceConfig } from '../types';
import { useAuth } from '../contexts/AuthContext';

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

export function parseInferenceConfig(data: any[][]): InferenceConfig[] {
    return data.map(item => ({
        id: item[0],
        json_name: item[1],
        pth_name: item[2],
        pth_upload_time: item[3],
    }));
}
const copyToClipboard = async (text: string) => {
    try {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
    } catch (err) {
        // 降级处理：使用传统方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        message.success('已复制到剪贴板');
    }
};
const PdpPathAnnotation: React.FC = () => {
    // 获取URL参数中的评测集ID
    const { user, isAuthenticated } = useAuth(); // 获取当前用户信息
    const { id } = useParams<{ id: string }>();
    useEffect(() => {
        if (!isAuthenticated) {
            console.log(user)
            message.error('请先登录后再进行标注操作');
            return;
        }
    }, [isAuthenticated]);
    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(400); // 减少左侧宽度
    const [rightSiderWidth, setRightSiderWidth] = useState(250);
    
    // 计算固定的中间区域宽度
    const fixedCenterWidth = 650; // 固定宽度，可以根据需要调整

    const [annotationStats, setAnnotationStats] = useState({
        total: 0,
        annotated: 0,
        good: 0,
        bad: 0,
        unknown: 0,
    });
    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [pklList, setPklList] = useState<EvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
    const [pdpPaths, setPdpPaths] = useState<Record<number, PdpPathInfo>>({});
    const [highlightPathIndex, setHighlightPathIndex] = useState<number | null>(null);
    const [loading, setLoading] = useState({
        pklList: false,
        paths: false,
        visualization: false,
        annotation: false,
        markDirty: false,
        configs: false,
        checkPkl: false,
    });
    const [pklPagination, setPklPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });
    const [searchKeyword, setSearchKeyword] = useState('');
    const [isDirtyData, setIsDirtyData] = useState(false);
    const [showFullyAnnotatedOnly, setShowFullyAnnotatedOnly] = useState(false);
    const [checkStatusFilter, setCheckStatusFilter] = useState<'all' | 'checked' | 'unchecked'>('all');
    
    // 修改VIN码和时间戳过滤相关状态为bag_name
    const [bagName, setBagName] = useState('');
    const [timeNs, setTimeNs] = useState('');
    const [timeRange, setTimeRange] = useState('');

    // 推理配置相关状态
    const [configs, setConfigs] = useState<InferenceConfig[]>([]);
    const [selectedConfig, setSelectedConfig] = useState<number | null>(null);
    const [configPagination, setConfigPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [pathInPickle, setHasPdpPaths] = useState(true); // 是否有PDP路径数据
    const [imageData, setImageData] = useState<string | null>(null);

    // 添加新的状态来存储ego2img矩阵
    const [ego2img, setEgo2img] = useState<number[][] | null>(null);
    const [egoYaw, setEgoYaw] = useState<number | null>(null);
    
    // 添加状态来存储最新的标注信息
    const [latestAnnotationInfo, setLatestAnnotationInfo] = useState<{
        employee_id: string;
        updated_at: string;
    } | null>(null);

    // 加载PKL文件列表，使用与EvaluationSetDetail相同的API
    const loadPklList = async (
        page = pklPagination.current, 
        pageSize = pklPagination.pageSize, 
        search = searchKeyword, 
        fullyAnnotatedOnly = showFullyAnnotatedOnly, 
        checkStatus = checkStatusFilter,
        bag_name = bagName,
        time_ns = timeNs,
        time_range = timeRange
    ) => {
        if (!id) return;

        setLoading(prev => ({ ...prev, pklList: true }));
        try {
            const params: any = {
                page,
                per_page: pageSize,
                search,
                fully_annotated_only: fullyAnnotatedOnly,
                check_status: checkStatus
            };

            // 只有当bag_name、时间戳和时间范围都不为空时，才添加这些参数
            if (bag_name && time_ns && time_range) {
                params.bag_name = bag_name;
                params.time_ns = time_ns;
                params.time_range = time_range;
            }

            const response = await axios.get(`/api/evaluation_sets/${id}`, { params });

            if (response.data.success) {
                const evaluationSetData: EvaluationSet = {
                    id: response.data.evaluation_set.id,
                    set_name: response.data.evaluation_set.name,
                    creator_name: response.data.evaluation_set.creator_name || '',
                    description: response.data.evaluation_set.description,
                    cases: response.data.cases || [],
                    case_count: response.data.case_count || 0,
                    created_at: response.data.evaluation_set.created_at
                };
                setEvaluationSet(evaluationSetData);
                setPklList(response.data.cases || []);
                setPklPagination({
                    current: response.data.page || page,
                    pageSize: response.data.per_page || pageSize,
                    total: response.data.case_count || 0
                });
            } else {
                message.error(response.data.error || '加载PKL列表失败');
            }
        } catch (error) {
            console.error('Failed to load PKL list:', error);
            message.error('加载PKL列表出错');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };
    const handleExportAnnotations = async () => {
        if (!id) {
            message.error('评测集ID无效');
            return;
        }

        try {
            // 方案1: 直接打开新窗口下载
            const exportUrl = `/api/annotation/export-annotations/${id}`;
            window.open(exportUrl, '_blank');

            // 方案2: 如果需要处理响应数据，可以使用axios
            // const response = await axios.get(`/api/annotation/export-annotations/${id}`);
            // if (response.data.success) {
            //     // 创建下载链接
            //     const dataStr = JSON.stringify(response.data, null, 2);
            //     const dataBlob = new Blob([dataStr], { type: 'application/json' });
            //     const url = URL.createObjectURL(dataBlob);
            //     const link = document.createElement('a');
            //     link.href = url;
            //     link.download = `annotations_set_${id}_${new Date().toISOString().split('T')[0]}.json`;
            //     document.body.appendChild(link);
            //     link.click();
            //     document.body.removeChild(link);
            //     URL.revokeObjectURL(url);
            //     message.success('标注数据导出成功');
            // } else {
            //     message.error('导出失败：' + (response.data.error || '未知错误'));
            // }
        } catch (error) {
            console.error('Failed to export annotations:', error);
            message.error('导出出错，请稍后再试');
        }
    };
    const handleTogglePklCheck = async (pkl: EvaluationCase, event: React.MouseEvent) => {
        event.stopPropagation(); // 阻止事件冒泡，避免触发PKL选择

        if (!id) {
            message.error('评测集ID无效');
            return;
        }

        setLoading(prev => ({ ...prev, checkPkl: true }));

        try {
            const isCurrentlyChecked = pkl.is_checked;
            const endpoint = isCurrentlyChecked
                ? `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`
                : `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`;

            const method = isCurrentlyChecked ? 'delete' : 'post';

            const response = await axios[method](endpoint);

            if (response.data.success) {
                // 更新PKL列表中的检查状态
                const updatedPklList = pklList.map(item => {
                    if (item.id === pkl.id) {
                        return {
                            ...item,
                            is_checked: !isCurrentlyChecked,
                            checked_at: isCurrentlyChecked ? undefined : new Date().toISOString(),
                            checked_by: isCurrentlyChecked ? undefined : user?.employee_id
                        };
                    }
                    return item;
                });

                setPklList(updatedPklList);

                // 如果当前选中的PKL就是被操作的PKL，也要更新selectedPkl状态
                if (selectedPkl?.id === pkl.id) {
                    setSelectedPkl({
                        ...selectedPkl,
                        is_checked: !isCurrentlyChecked,
                        checked_at: isCurrentlyChecked ? undefined : new Date().toISOString(),
                        checked_by: isCurrentlyChecked ? undefined : user?.employee_id
                    });
                }

                message.success(
                    isCurrentlyChecked ? 'PKL检查标记已取消' : 'PKL已标记为已检查'
                );
            } else {
                message.error('操作失败：' + (response.data.error || '未知错误'));
            }
        } catch (error) {
            console.error('Failed to toggle PKL check status:', error);
            message.error('操作出错，请稍后再试');
        } finally {
            setLoading(prev => ({ ...prev, checkPkl: false }));
        }
    };

    // 加载推理配置列表
    const fetchConfigs = async (page = configPagination.current, pageSize = configPagination.pageSize) => {
        setLoading(prev => ({ ...prev, configs: true }));
        try {
            const response = await axios.get('/api/inference_config', {
                params: { page, per_page: pageSize }
            });
            const parsedConfigs = parseInferenceConfig(response.data.configs || response.data);
            setConfigs(parsedConfigs || []);

            // 如果API返回了总数，更新分页信息
            if (response.data.total !== undefined) {
                setConfigPagination(prev => ({ ...prev, total: response.data.total }));
            }
        } catch (error) {
            console.error('Failed to fetch inference configs:', error);
            message.error('获取推理配置失败，请稍后再试');
        } finally {
            setLoading(prev => ({ ...prev, configs: false }));
        }
    };

    // 初始加载
    useEffect(() => {
        if (id) {
            loadPklList();
            fetchConfigs();
        }
    }, [id]);

    // 处理配置分页
    const handleConfigPageChange = async (page: number, pageSize: number) => {
        setConfigPagination(prev => ({ ...prev, current: page, pageSize }));
        await fetchConfigs(page, pageSize);
    };

    // 处理配置选择
    const handleConfigSelect = (configId: number) => {
        setSelectedConfig(configId);
        // 如果有选中的案例，获取对应配置的轨迹数据
        // if (selectedPkl) {
        //     fetchTrajectories(selectedPkl.id, configId);
        // }
    };

    // 当选择PKL文件时加载PDP路径
    const handlePklSelect = async (pkl: EvaluationCase) => {
        setSelectedPkl(pkl);
        setHighlightPathIndex(null); // 清除之前的高亮
        setImageData(null); // 清除上一次的图像数据
        setLatestAnnotationInfo(null); // 清除之前的最新标注信息
        setLoading(prev => ({ ...prev, paths: true }));

        try {
            const response = await axios.get(`/api/annotation/pdp-paths/${pkl.id}`, {
                params: {
                    evaluation_set_id: id
                }
            });
            if (response.data.success) {
                if (response.data.paths && response.data.paths.length > 0) {
                    const pathsDict: Record<number, PdpPathInfo> = {};
                    response.data.paths.forEach((path: PdpPathInfo) => {
                        pathsDict[path.index] = path;
                    });
                    setPdpPaths(pathsDict);
                    setIsDirtyData(response.data.is_dirty || false);
                    setHasPdpPaths(true);

                    if (response.data.image_data) {
                        setImageData(response.data.image_data);
                    }
                    if (response.data.ego2img) {
                        setEgo2img(response.data.ego2img);
                        // console.log('🔄 设置ego2img矩阵:', response.data.ego2img);
                    } else {
                        setEgo2img(null);
                        // console.log('⚠️ 未获取到ego2img矩阵');
                    }
                    if (response.data.ego_yaw) {
                        setEgoYaw(response.data.ego_yaw);
                        // console.log('✅ 设置egoYaw成功:', response.data.ego_yaw, '弧度 (', (response.data.ego_yaw * 180 / Math.PI).toFixed(1), '度)');
                    } else {
                        setEgoYaw(null);
                        // console.log('⚠️ 未获取到egoYaw数据');
                    }
                    
                    // 设置最新的标注信息
                    if (response.data.latest_annotation_info) {
                        setLatestAnnotationInfo(response.data.latest_annotation_info);
                    }

                    // 计算标注统计信息
                    const paths = response.data.paths;
                    const total = paths.length;
                    let annotated = 0;
                    let good = 0;
                    let bad = 0;
                    let unknown = 0;

                    paths.forEach((path: PdpPathInfo) => {
                        if (path.annotation) {
                            annotated++;
                            if (path.annotation.annotation === 'good') {
                                good++;
                            } else if (path.annotation.annotation === 'bad') {
                                bad++;
                            } else if (path.annotation.annotation === 'unknown') {
                                unknown++;
                            }
                        }
                    });

                    setAnnotationStats({
                        total,
                        annotated,
                        good,
                        bad,
                        unknown
                    });
                } else {
                    // 没有PDP路径数据，需要通过配置获取
                    setHasPdpPaths(false);
                    setPdpPaths({});
                    setAnnotationStats({
                        total: 0,
                        annotated: 0,
                        good: 0,
                        bad: 0,
                        unknown: 0
                    });

                    // // 如果已选择配置，立即获取轨迹
                    // if (selectedConfig) {
                    //     fetchTrajectories(pkl.id, selectedConfig);
                    // }
                }
            } else {
                setPdpPaths({});
                setHasPdpPaths(false);
                setAnnotationStats({
                    total: 0,
                    annotated: 0,
                    good: 0,
                    bad: 0,
                    unknown: 0
                });
                message.error('加载PDP路径失败');
            }
        } finally {
            setLoading(prev => ({ ...prev, paths: false }));
        }
    };

    // 加载可视化数据
    const loadVisualizationData = async (pkl: EvaluationCase) => {
        try {
            setLoading(prev => ({ ...prev, visualization: true }));
            
            const response = await axios.get(`/api/annotation/pdp-paths/${pkl.id}`);
            
            if (response.data.success) {
                // 设置路径数据
                setPdpPaths(response.data.pdp_paths || {});
                console.log('🔍 完整响应数据:', response.data);
                // 设置图像数据
                if (response.data.image_data) {
                    setImageData(response.data.image_data);
                }
                
                // 设置ego2img矩阵
                if (response.data.ego2img) {
                    setEgo2img(response.data.ego2img);
                    console.log('🔄 设置ego2img矩阵:', response.data.ego2img);
                } else {
                    setEgo2img(null);
                    console.log('⚠️ 未获取到ego2img矩阵');
                }
            }
        } catch (error) {
            console.error('加载可视化数据失败:', error);
            message.error('加载可视化数据失败');
        } finally {
            setLoading(prev => ({ ...prev, visualization: false }));
        }
    };

    // 高亮某条路径
    const handleHighlightPath = (pathIndex: number) => {
        setHighlightPathIndex(pathIndex === highlightPathIndex ? null : pathIndex);
    };

    // 提交标注
    const handleAnnotate = async (pathIndex: number, annotation: string) => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件并输入标注者姓名');
            return;
        }

        // if (!isAuthenticated || !user) {
        //     message.error('用户未登录，无法进行标注操作');
        //     return;
        // }

        // 检查是否需要删除标注（点击了当前已选择的按钮）
        const currentAnnotation = pdpPaths[pathIndex]?.annotation?.annotation;
        const isDeleteAction = currentAnnotation === annotation;

        setLoading(prev => ({ ...prev, annotation: true }));
        try {
            const response = await axios.post('/api/annotation/pdp-paths', {
                pkl_id: selectedPkl.id,
                path_index: pathIndex,
                annotation,
                delete_annotation: isDeleteAction, // 新增参数，标识是删除操作
                inference_config_id: !pathInPickle ? selectedConfig : undefined, // 如果是从配置获取的路径，传递配置ID
                evaluation_set_id: id,
                employee_id: user?.employee_id || null, // 添加工号信息
            });

            if (response.data.success) {
                // 更新路径列表中的标注状态
                const updatedPaths = { ...pdpPaths };

                const oldAnnotation = updatedPaths[pathIndex].annotation?.annotation;

                // 如果是删除操作，设置annotation为null，否则更新为新的标注
                if (isDeleteAction) {
                    updatedPaths[pathIndex] = {
                        ...updatedPaths[pathIndex],
                        annotation: undefined
                    };
                    message.success('标注已删除');
                } else {
                    updatedPaths[pathIndex] = {
                        ...updatedPaths[pathIndex],
                        annotation: {
                            annotation,
                            employee_id: user?.employee_id || undefined,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString(),
                        }
                    };
                    message.success('标注保存成功');
                }

                setPdpPaths(updatedPaths);

                // 更新统计信息
                setAnnotationStats(prev => {
                    const newStats = { ...prev };

                    // 如果是删除标注
                    if (isDeleteAction && oldAnnotation) {
                        newStats.annotated--;
                        if (oldAnnotation === 'good') newStats.good--;
                        else if (oldAnnotation === 'bad') newStats.bad--;
                        else if (oldAnnotation === 'unknown') newStats.unknown--;
                    }
                    // 如果是新增标注
                    else if (!oldAnnotation && !isDeleteAction) {
                        newStats.annotated++;
                        if (annotation === 'good') newStats.good++;
                        else if (annotation === 'bad') newStats.bad++;
                        else if (annotation === 'unknown') newStats.unknown++;
                    }
                    // 如果是修改标注
                    else if (oldAnnotation !== annotation && !isDeleteAction) {
                        if (oldAnnotation === 'good') newStats.good--;
                        else if (oldAnnotation === 'bad') newStats.bad--;
                        else if (oldAnnotation === 'unknown') newStats.unknown--;

                        if (annotation === 'good') newStats.good++;
                        else if (annotation === 'bad') newStats.bad++;
                        else if (annotation === 'unknown') newStats.unknown++;
                    }

                    return newStats;
                });
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to save/delete annotation:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 处理标记为脏数据
    const handleMarkAsDirty = async () => {
        if (!selectedPkl) {
            message.error('请先选择PKL文件');
            return;
        }

        setLoading(prev => ({ ...prev, markDirty: true }));
        try {
            const response = await axios.post('/api/annotation/mark-dirty', {
                pkl_id: selectedPkl.id,
                is_dirty: !isDirtyData // 反转当前状态
            });

            if (response.data.success) {
                setIsDirtyData(response.data.is_dirty);
                message.success(
                    response.data.is_dirty
                        ? '已标记为脏数据'
                        : '已取消脏数据标记'
                );

                // 更新列表中当前项的脏数据状态
                const updatedPklList = pklList.map(pkl => {
                    if (pkl.id === selectedPkl.id) {
                        return { ...pkl, dirty_data: response.data.is_dirty };
                    }
                    return pkl;
                });
                setPklList(updatedPklList);
            } else {
                message.error('操作失败');
            }
        } catch (error) {
            console.error('Failed to mark as dirty data:', error);
            message.error('操作出错');
        } finally {
            setLoading(prev => ({ ...prev, markDirty: false }));
        }
    };

    // 处理分页变化
    const handlePklPageChange = (page: number, pageSize?: number) => {
        loadPklList(page, pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };

    // 处理搜索
    const handleSearch = () => {
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleFilterToggle = () => {
        const newFilterState = !showFullyAnnotatedOnly;
        setShowFullyAnnotatedOnly(newFilterState);
        loadPklList(1, pklPagination.pageSize, searchKeyword, newFilterState, checkStatusFilter, bagName, timeNs, timeRange);
    };
    
    const handleCheckStatusFilter = (status: 'all' | 'checked' | 'unchecked') => {
        setCheckStatusFilter(status);
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, status, bagName, timeNs, timeRange);
    };

    // 修改：清空bag_name和时间戳过滤
    const handleClearBagTimeFilter = () => {
        setBagName('');
        setTimeNs('');
        setTimeRange('');
        loadPklList(1, pklPagination.pageSize, searchKeyword, showFullyAnnotatedOnly, checkStatusFilter, '', '', '');
    };

    return (
        <div className="pdp-path-annotation-layout" style={{ display: 'flex', height: '100vh' }}>
            {/* 左侧PKL列表 - 可调整宽度 */}
            <ResizableSider
                width={leftSiderWidth}
                minWidth={200}
                maxWidth={600}
                onResize={setLeftSiderWidth}
                position="left"
                className="pkl-list-sider"
            >
                <div className="pkl-list-header">
                    <Space direction="vertical" style={{ width: '100%' }} size="small">
                        {/* 压缩评测集名称显示区域 */}
                        {evaluationSet && (
                            <div style={{ 
                                marginBottom: '4px', 
                                padding: '4px 6px', 
                                background: '#f0f0f0', 
                                borderRadius: '3px',
                                border: '1px solid #d9d9d9'
                            }}>
                                <Text strong style={{ fontSize: '12px', color: '#1890ff', lineHeight: '1.2' }}>
                                    {evaluationSet.set_name}
                                </Text>
                            </div>
                        )}
                        
                        {/* 压缩搜索框 */}
                        <Input
                            placeholder="搜索PKL文件"
                            prefix={<SearchOutlined />}
                            value={searchKeyword}
                            onChange={e => setSearchKeyword(e.target.value)}
                            onPressEnter={handleSearch}
                            size="small"
                            style={{ marginBottom: '4px' }}
                        />
                        
                        {/* 修改为bag_name和时间戳过滤输入框 */}
                        <div style={{ border: '1px solid #d9d9d9', padding: '6px', borderRadius: '4px' }}>
                            <Text strong style={{ fontSize: '11px', color: '#666' }}>bag_name时间戳过滤:</Text>
                            <Input
                                placeholder="bag_name (例: LFZ63AZ52SD000199_record_data_2025_05_04_08_40_39)"
                                value={bagName}
                                onChange={e => setBagName(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间戳(纳秒)"
                                value={timeNs}
                                onChange={e => setTimeNs(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Input
                                placeholder="时间范围(秒)"
                                value={timeRange}
                                onChange={e => setTimeRange(e.target.value)}
                                size="small"
                                style={{ marginTop: '3px' }}
                            />
                            <Space size="small" style={{ marginTop: '3px', width: '100%' }}>
                                <Button
                                    type="primary"
                                    size="small"
                                    onClick={handleSearch}
                                    style={{ flex: 1 }}
                                >
                                    应用过滤
                                </Button>
                                <Button
                                    size="small"
                                    onClick={handleClearBagTimeFilter}
                                    style={{ flex: 1 }}
                                >
                                    清空
                                </Button>
                            </Space>
                        </div>

                        <Space size="small" style={{ marginTop: '4px' }}>
                            <Button
                                type={showFullyAnnotatedOnly ? "primary" : "default"}
                                icon={<CheckCircleOutlined />}
                                onClick={handleFilterToggle}
                                size="small"
                            >
                                {showFullyAnnotatedOnly ? "显示全部" : "仅显示完全标注"}
                            </Button>
                            <Radio.Group
                                value={checkStatusFilter}
                                onChange={e => handleCheckStatusFilter(e.target.value)}
                                size="small"
                                buttonStyle="solid"
                            >
                                <Radio.Button value="all">全部</Radio.Button>
                                <Radio.Button value="checked">已检查</Radio.Button>
                                <Radio.Button value="unchecked">未检查</Radio.Button>
                            </Radio.Group>
                            <Button
                                type="default"
                                icon={<SearchOutlined />}
                                onClick={handleSearch}
                                size="small"
                            >
                                搜索
                            </Button>
                        </Space>
                        <Button
                            type="default"
                            icon={<ExportOutlined />}
                            onClick={handleExportAnnotations}
                            size="small"
                            style={{ width: '100%', marginTop: '4px' }}
                        >
                            导出标注数据
                        </Button>
                    </Space>
                </div>

                <List
                    loading={loading.pklList}
                    dataSource={pklList}
                    renderItem={item => (
                        <List.Item
                            className={selectedPkl?.id === item.id ? 'pkl-item selected' : 'pkl-item'}
                            onClick={() => handlePklSelect(item)}
                            style={{ display: 'flex', alignItems: 'flex-start' }}
                        >
                            <div className="pkl-item-content" style={{ flex: 1, minWidth: 0 }}>
                                <div className="pkl-name" style={{
                                    fontSize: '11px',
                                    lineHeight: '1.3',
                                    wordBreak: 'break-all',
                                    whiteSpace: 'normal',
                                    maxWidth: '100%',
                                    marginBottom: '4px'
                                }}>
                                    {item.pkl_name}
                                    {item.dirty_data && (
                                        <Tag color="red" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <WarningOutlined /> 脏数据
                                        </Tag>
                                    )}
                                    {item.is_checked && (
                                        <Tag color="green" style={{ marginLeft: 5, fontSize: '10px' }}>
                                            <CheckOutlined /> 已检查
                                        </Tag>
                                    )}
                                </div>
                                {item.is_checked && item.checked_by && (
                                    <div style={{
                                        fontSize: '10px',
                                        color: '#999',
                                        lineHeight: '1.2',
                                        wordBreak: 'break-all',
                                        whiteSpace: 'normal'
                                    }}>
                                        {/* 检查人: {item.checked_by} */}
                                    </div>
                                )}
                                
                                
                            </div>
                            <div style={{ display: 'flex', flexDirection: 'row', gap: 4, marginLeft: 8 }}>
                                <Button
                                    type={item.is_checked ? "primary" : "default"}
                                    icon={<CheckOutlined />}
                                    size="small"
                                    loading={loading.checkPkl}
                                    onClick={(e) => handleTogglePklCheck(item, e)}
                                    title={item.is_checked ? "取消检查标记" : "标记为已检查"}
                                    style={{
                                        flexShrink: 0,
                                        backgroundColor: item.is_checked ? '#52c41a' : undefined,
                                        borderColor: item.is_checked ? '#52c41a' : undefined,
                                    }}
                                />
                                <Button
                                    type="text"
                                    icon={<CopyOutlined />}
                                    size="small"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const fullPath = `${item.pkl_dir}/${item.pkl_name}`;
                                        copyToClipboard(fullPath);
                                    }}
                                    title="复制文件路径"
                                    style={{ flexShrink: 0 }}
                                />
                            </div>
                        </List.Item>
                    )}
                />

                <div className="pkl-pagination">
                    <Pagination
                        current={pklPagination.current}
                        pageSize={pklPagination.pageSize}
                        total={pklPagination.total}
                        onChange={handlePklPageChange}
                        showSizeChanger={false}
                        size="small"
                        simple
                    />
                </div>
            </ResizableSider>

            {/* 中间可视化区域 - 还原为flex自适应 */}
            <div 
                className="visualization-content"
                style={{ 
                    flex: 1, // 恢复flex自适应
                    background: '#fff',
                    borderRight: '1px solid #f0f0f0',
                    overflow: 'hidden'
                }}
            >
                {selectedPkl ? (
                    <div className="visualization-container" style={{ width: '100%', height: '100%' }}>
                        <div className="visualization-area" style={{ width: '100%', height: '100%' }}>
                            {loading.visualization ? (
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'center', 
                                    alignItems: 'center', 
                                    height: '100%' 
                                }}>
                                    <Spin tip="加载可视化..." />
                                </div>
                            ) : (
                                <div style={{ width: '100%', height: '100%' }}>
                                    {/* 显示最新的标注信息 */}
                                    {latestAnnotationInfo && (
                                        <div style={{ 
                                            padding: '8px', 
                                            background: '#f6ffed', 
                                            borderBottom: '1px solid #f0f0f0',
                                            fontSize: '12px'
                                        }}>
                                            <Text strong>最新标注信息：</Text>
                                            <Text> 标注员 {latestAnnotationInfo.employee_id} </Text>
                                            <Text>于 {new Date(latestAnnotationInfo.updated_at).toLocaleString('zh-CN')} 更新</Text>
                                        </div>
                                    )}
                                    
                                    {imageData && (
                                        <div className="e2e-image-container" style={{ 
                                            width: '100%', 
                                            display: 'flex',          // 添加flex布局
                                            justifyContent: 'center', // 水平居中
                                            alignItems: 'center',     // 垂直居中
                                            marginBottom: '10px'
                                        }}>
                                            {/* 替换原来的img标签为新的ImageWithPaths组件 */}
                                            <ImageWithPaths
                                                imageData={imageData}
                                                pdpPaths={pdpPaths}
                                                highlightPathIndex={highlightPathIndex}
                                                ego2img={ego2img}
                                                egoYaw={egoYaw || 0} // 提供默认值，或者使用条件渲染
                                                style={{ 
                                                    maxWidth: '100%',
                                                    maxHeight: '200px'
                                                }}
                                            />
                                        </div>
                                    )}
                                    <div style={{ 
                                        width: '100%', 
                                        height: imageData ? 'calc(100% - 220px)' : '100%',
                                        minHeight: '400px'
                                    }}>
                                        <PickleVisualizer
                                            evaluationCase={selectedPkl}
                                            highlightPathIndex={highlightPathIndex}
                                            pdpPaths={pdpPaths}
                                            height={imageData ? 'calc(100vh - 320px)' : '60vh'}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        color: '#999'
                    }}>
                        <Text>请从左侧选择一个PKL文件</Text>
                    </div>
                )}
            </div>

            {/* 右侧标注面板 - 可调整宽度 */}
            <ResizableSider
                width={rightSiderWidth}
                minWidth={250}
                maxWidth={500}
                onResize={setRightSiderWidth}
                position="right"
                className="annotation-sider"
            >
                {/* 右侧内容保持不变 */}
                <div className="annotation-panel">
                    <div className="annotation-header">
                        {selectedPkl && (
                            <div className="bad-data-button-container">
                                <Button
                                    danger={!isDirtyData}
                                    type={isDirtyData ? "default" : "primary"}
                                    icon={<WarningOutlined />}
                                    onClick={handleMarkAsDirty}
                                    loading={loading.markDirty}
                                >
                                    {isDirtyData ? "取消脏数据标记" : "标记为脏数据"}
                                </Button>
                            </div>
                        )}
                        {selectedPkl && !loading.paths && Object.keys(pdpPaths).length > 0 && (
                            <div className="annotation-stats">
                                <div className="stats-summary">
                                    <Tag color="success" icon={<CheckCircleOutlined />}>好: {annotationStats.good}</Tag>
                                    <Tag color="error" icon={<CloseCircleOutlined />}>差: {annotationStats.bad}</Tag>
                                    <Tag color="default" icon={<QuestionCircleOutlined />}>未知: {annotationStats.unknown}</Tag>
                                </div>
                            </div>
                        )}
                    </div>

                    {!selectedPkl ? (
                        <div className="empty-annotation">
                            <Text>请先选择一个PKL文件</Text>
                        </div>
                    ) : loading.paths ? (
                        <div className="loading-paths">
                            <Spin tip="加载路径数据..." />
                        </div>
                    ) : !pathInPickle && Object.keys(pdpPaths).length === 0 ? (
                        <div className="config-selector-container">
                            <Text>该PKL文件中没有PDP路径数据，请选择一个推理配置查看路径</Text>
                            <div className="config-selector">
                                <InferenceConfigSelector
                                    configs={configs}
                                    selectedConfig={selectedConfig}
                                    onConfigSelect={handleConfigSelect}
                                    loading={loading.configs}
                                    mode="single"
                                    pagination={{
                                        current: configPagination.current,
                                        pageSize: configPagination.pageSize,
                                        total: configPagination.total,
                                        onChange: handleConfigPageChange
                                    }}
                                    evaluationSetId={id}
                                />
                            </div>
                        </div>
                    ) : Object.keys(pdpPaths).length === 0 ? (
                        <div className="no-paths">
                            <Text>未找到PDP路径数据</Text>
                        </div>
                    ) : (
                        <div className="paths-list">
                            {Object.values(pdpPaths).sort((a, b) => a.index - b.index).map((path) => (
                                <div className="path-card-container" key={path.index}>
                                    <Card
                                        className={highlightPathIndex === path.index ? 'path-card highlighted' : 'path-card'}
                                        size="small"
                                        onClick={() => handleHighlightPath(path.index)}
                                        hoverable
                                    >
                                        <div className="path-info">
                                            <div className="path-title">
                                                <Tag color="blue">路径 {path.index + 1}</Tag>
                                            </div>
                                        </div>
                                    </Card>

                                    <div className="path-actions" onClick={e => e.stopPropagation()}>
                                        <Radio.Group
                                            value={path.annotation?.annotation}
                                            onChange={e => {
                                                e.stopPropagation();
                                                handleAnnotate(path.index, e.target.value);
                                            }}
                                            buttonStyle="solid"
                                        >
                                            <Radio.Button
                                                value="good"
                                                className={path.annotation?.annotation === 'good' ? 'good-selected' : ''}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (path.annotation?.annotation === 'good') {
                                                        handleAnnotate(path.index, 'good');
                                                    }
                                                }}
                                            >
                                                <CheckCircleOutlined /> 好
                                            </Radio.Button>
                                            <Radio.Button
                                                value="bad"
                                                className={path.annotation?.annotation === 'bad' ? 'bad-selected' : ''}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (path.annotation?.annotation === 'bad') {
                                                        handleAnnotate(path.index, 'bad');
                                                    }
                                                }}
                                            >
                                                <CloseCircleOutlined /> 差
                                            </Radio.Button>
                                            <Radio.Button
                                                value="unknown"
                                                className={path.annotation?.annotation === 'unknown' ? 'unknown-selected' : ''}
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    if (path.annotation?.annotation === 'unknown') {
                                                        handleAnnotate(path.index, 'unknown');
                                                    }
                                                }}
                                            >
                                                <QuestionCircleOutlined /> 未知
                                            </Radio.Button>
                                        </Radio.Group>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </ResizableSider>
        </div>
    );
};

export default PdpPathAnnotation;